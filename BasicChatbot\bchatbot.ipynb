### BUILD BASIC CHATBOT 
# This script builds a basic chatbot using the OpenAI API.

# 3 main components of langgraph 
# Edge
# Node
# State  
# LLM good with content Generation

'''
You have a problem statement wherein you're creating a blog out of a YT video
yt---->get the transcript
transcript---->Title 
title and transcript -----> become the body of the blog

To achieve this there are 2 ways

Graph API  --->Easier to use
Functional API  


THIS IS CALLED A 

*******----STATE GRAPH----******

Start 
|
|-------> Edge
|
V

NODE  
__________________________________________________________________
implimentation takes the yt video and converts it into transcript |
__________________________________________________________________|
|
|--------> Give Transcript
|
V                                                                                    Tramscript
NODE                                                                                    |
__________________________________________________________________                      V
Title Generator                                                   |       -------> LLM + prompt
__________________________________________________________________|                     |
|                                                                                       V       
|---------> Title, Transcript generator                                               Output            
|
V
NODE                                                                                    |    
__________________________________________________________________                      V    
Content Generator                                                 |                 LLM + prompt
__________________________________________________________________|                     |
|                                                                                       V   
|                                                                                     Output  
|
V
END (OUTPUT)
'''

from typing import Annotated
from typing_extensions import TypedDict
from langgraph.graph import StateGraph, START, END
from langgraph.graph.message import add_messages   # reducer 

'''
<Start>         StateGRAPH
    |
|CHATBOT|       LLM+Prompt        
    |
<END>               O/p

Everytime a message is given it should maintain a list of messages context
message=[_,_,_,_,_,] type list we keep on appending the messages to keep the context intact 

to append we use reducers for eg add_messages - adds the message to the list
'''

'''Message have type list. the add_message function in the annotation defines how this state key should be updated 
 in this case it appends rather than overwriting it 
 we inherted typeddict so that the class state is returned as a type dictionary'''
class State(TypedDict):
    messages=Annotated[list,add_messages]  #add_messages is called as a reducer which appends and not replaces

graph_builder=StateGraph(State)

graph_builder

import os
from dotenv import load_dotenv
load_dotenv()

from langchain_groq import ChatGroq
from langchain.chat_models import init_chat_model

llm=ChatGroq(model="llama3-8b-8192")

llm

llm=init_chat_model("groq:llama3-8b-8192")
llm

## Node Functionality
def chatbot(state:State):
    return {"messages":[llm.invoke(state["messages"])]}

graph_builder=StateGraph(State)
graph_builder.add_node("llmchatbot",chatbot)   # adding node

graph_builder.add_edge(START,"llmchatbot")     # adding START edge
graph_builder.add_edge("llmchatbot",END)       # adding End edge

graph=graph_builder.compile()   # Compile Graph

# Visualize the Graph

from IPython.display import Image,display
try:
    display(Image(grpah.get_graph().draw_mermaid_png()))
except:
    pass


graph.invoke({"messages":"Hi"})

